# Linux等保基线核查工具需求文档

## 1. 项目概述

### 1.1 项目背景
随着网络安全等级保护制度的深入实施，企业和组织需要定期对Linux系统进行等保基线合规性检查。传统的手工检查方式效率低下且容易出错，急需一款自动化的等保基线核查工具。

### 1.2 项目目标
开发一款基于Go语言和Fyne GUI框架的Linux等保基线核查工具，实现：
- 自动化检查Linux系统等保基线合规性
- 提供直观的图形化用户界面
- 生成详细的检查报告
- 支持自定义检查规则
- 提高等保合规检查效率

### 1.3 项目范围
- 支持主流Linux发行版（CentOS、Ubuntu、RHEL、Debian等）
- 覆盖等保2.0基本要求和增强要求
- 提供桌面GUI应用程序
- 支持本地和远程系统检查

## 2. 功能需求

### 2.1 核心功能模块

#### 2.1.1 系统基础安全检查
- **账户管理检查**
  - 默认账户状态检查
  - 账户密码策略验证
  - 账户权限分配检查
  - 空密码账户检测

- **文件系统安全检查**
  - 关键文件权限检查
  - 敏感目录访问控制
  - 文件完整性验证
  - 临时文件清理策略

- **网络安全配置检查**
  - 网络服务端口检查
  - 防火墙配置验证
  - SSH配置安全检查
  - 网络协议安全设置

#### 2.1.2 系统服务安全检查
- **系统服务管理**
  - 不必要服务检测
  - 服务启动权限检查
  - 服务配置安全验证
  - 系统进程监控

- **日志审计配置**
  - 系统日志配置检查
  - 审计策略验证
  - 日志存储安全检查
  - 日志轮转配置

#### 2.1.3 安全策略检查
- **访问控制策略**
  - SELinux/AppArmor状态检查
  - 文件访问控制列表
  - 用户权限分离检查
  - 特权账户管理

- **系统加固检查**
  - 内核参数安全配置
  - 系统补丁更新状态
  - 安全模块配置
  - 系统资源限制

### 2.2 用户界面功能

#### 2.2.1 主界面设计
- **菜单栏**：文件、编辑、工具、帮助
- **工具栏**：快速操作按钮（开始检查、停止检查、导出报告等）
- **检查项目树**：分类显示所有检查项目
- **结果显示区**：实时显示检查结果
- **详情面板**：显示检查项详细信息和修复建议
- **状态栏**：显示检查进度和系统状态

#### 2.2.2 检查配置界面
- 检查项目选择（支持全选、分类选择、自定义选择）
- 检查参数配置（超时时间、并发数等）
- 目标系统配置（本地/远程系统连接信息）
- 检查模式选择（快速检查/完整检查/自定义检查）

#### 2.2.3 报告管理界面
- 历史检查记录查看
- 报告对比功能
- 报告导出（HTML、PDF、Excel格式）
- 报告模板自定义

### 2.3 规则管理功能
- **规则编辑器**：可视化规则编辑界面
- **规则导入导出**：支持规则文件的导入导出
- **规则版本管理**：规则版本控制和回滚
- **自定义规则**：支持用户自定义检查规则

## 3. 非功能需求

### 3.1 性能要求
- 单次完整检查时间不超过10分钟
- 支持并发检查，提高检查效率
- 内存占用不超过500MB
- 支持大型系统（1000+检查项）

### 3.2 可用性要求
- 界面简洁直观，操作流程清晰
- 提供详细的帮助文档和操作指南
- 支持中英文界面切换
- 错误信息友好，提供解决建议

### 3.3 兼容性要求
- 支持主流Linux发行版
- 支持不同架构（x86_64、ARM64）
- 向后兼容旧版本规则文件
- 支持跨平台运行（Linux、Windows、macOS）

### 3.4 安全要求
- 检查过程不影响系统正常运行
- 敏感信息加密存储
- 支持权限验证和访问控制
- 检查日志完整记录

## 4. 技术架构

### 4.1 技术选型
- **开发语言**：Go 1.19+
- **GUI框架**：Fyne v2.4+
- **配置格式**：YAML/JSON
- **报告生成**：HTML模板 + PDF导出
- **数据存储**：本地文件存储

### 4.2 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GUI Layer     │    │  Business Layer │    │   Data Layer    │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Main Window │ │    │ │ Check Engine│ │    │ │ Rule Files  │ │
│ │ Config UI   │ │◄──►│ │ Report Gen  │ │◄──►│ │ Config Data │ │
│ │ Report UI   │ │    │ │ Rule Manager│ │    │ │ Result Data │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 4.3 项目结构
```
mlps-checker/
├── cmd/
│   └── main.go                 # 程序入口
├── internal/
│   ├── gui/                    # GUI界面
│   │   ├── main_window.go
│   │   ├── config_dialog.go
│   │   └── report_viewer.go
│   ├── checker/                # 检查引擎
│   │   ├── engine.go
│   │   ├── rules.go
│   │   └── executor.go
│   ├── report/                 # 报告生成
│   │   ├── generator.go
│   │   └── templates/
│   └── config/                 # 配置管理
│       ├── config.go
│       └── settings.go
├── rules/                      # 检查规则
│   ├── basic/
│   ├── enhanced/
│   └── custom/
├── templates/                  # 报告模板
├── assets/                     # 资源文件
├── docs/                       # 文档
├── go.mod
└── go.sum
```

## 5. 开发计划

### 5.1 开发阶段

#### 第一阶段：基础框架开发（2周）
- 项目初始化和环境搭建
- 基础GUI框架搭建
- 核心数据结构设计
- 基本的检查引擎框架

#### 第二阶段：核心功能开发（4周）
- 检查规则引擎实现
- 主要检查模块开发
- GUI界面完善
- 基础报告生成功能

#### 第三阶段：功能完善（3周）
- 高级检查功能实现
- 报告格式优化
- 规则管理功能
- 错误处理和异常管理

#### 第四阶段：测试和优化（2周）
- 单元测试和集成测试
- 性能优化
- 用户体验优化
- 文档编写

### 5.2 里程碑
- **M1**：基础框架完成，可运行基本GUI
- **M2**：核心检查功能完成，可执行基本检查
- **M3**：完整功能实现，可生成检查报告
- **M4**：测试完成，可发布Beta版本

## 6. 测试策略

### 6.1 测试类型
- **单元测试**：覆盖核心业务逻辑
- **集成测试**：测试模块间交互
- **系统测试**：完整功能流程测试
- **兼容性测试**：多平台兼容性验证

### 6.2 测试环境
- 多个Linux发行版测试环境
- 不同版本系统测试
- 虚拟化环境测试
- 物理机环境验证

## 7. 部署要求

### 7.1 运行环境
- Linux系统（推荐CentOS 7+/Ubuntu 18.04+）
- 最小内存：512MB
- 磁盘空间：100MB
- 网络连接（用于远程检查）

### 7.2 部署方式
- 单文件可执行程序
- 支持绿色安装
- 提供安装包（deb/rpm）
- 支持容器化部署

## 8. 风险评估

### 8.1 技术风险
- Fyne框架兼容性问题
- 系统权限获取限制
- 不同Linux发行版差异

### 8.2 缓解措施
- 充分的技术调研和原型验证
- 完善的错误处理机制
- 广泛的兼容性测试
- 详细的用户文档和FAQ

---

**文档版本**：v1.0  
**创建日期**：2025-01-20  
**最后更新**：2025-01-20
