package report

import (
	"bytes"
	"encoding/json"
	"html/template"
	"mlps-checker/internal/checker"
	"os"
	"path/filepath"
	"time"
)

type Generator struct {
	templates map[string]*template.Template
}

type ReportData struct {
	Session     *checker.CheckSession `json:"session"`
	GeneratedAt time.Time             `json:"generated_at"`
	Summary     *ReportSummary        `json:"summary"`
}

type ReportSummary struct {
	TotalRules    int     `json:"total_rules"`
	PassedRules   int     `json:"passed_rules"`
	FailedRules   int     `json:"failed_rules"`
	ErrorRules    int     `json:"error_rules"`
	SkippedRules  int     `json:"skipped_rules"`
	PassRate      float64 `json:"pass_rate"`
	FailRate      float64 `json:"fail_rate"`
	ErrorRate     float64 `json:"error_rate"`
	Duration      string  `json:"duration"`
}

func NewGenerator() *Generator {
	return &Generator{
		templates: make(map[string]*template.Template),
	}
}

func (g *Generator) LoadTemplates(templatesPath string) error {
	return filepath.Walk(templatesPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if filepath.Ext(path) == ".html" {
			name := filepath.Base(path)
			tmpl, err := template.ParseFiles(path)
			if err != nil {
				return err
			}
			g.templates[name] = tmpl
		}

		return nil
	})
}

func (g *Generator) GenerateHTML(session *checker.CheckSession, outputPath string) error {
	data := g.prepareReportData(session)

	htmlTemplate := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linux等保基线核查报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .summary { display: flex; justify-content: space-around; margin-bottom: 20px; }
        .summary-item { text-align: center; padding: 10px; background-color: #e9ecef; border-radius: 5px; }
        .results { margin-top: 20px; }
        .result-item { border: 1px solid #ddd; margin-bottom: 10px; padding: 15px; border-radius: 5px; }
        .passed { border-left: 5px solid #28a745; }
        .failed { border-left: 5px solid #dc3545; }
        .error { border-left: 5px solid #ffc107; }
        .rule-name { font-weight: bold; font-size: 16px; margin-bottom: 5px; }
        .rule-desc { color: #666; margin-bottom: 10px; }
        .rule-output { background-color: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Linux等保基线核查报告</h1>
        <p><strong>检查任务:</strong> {{.Session.Name}}</p>
        <p><strong>开始时间:</strong> {{.Session.StartTime.Format "2006-01-02 15:04:05"}}</p>
        <p><strong>结束时间:</strong> {{.Session.EndTime.Format "2006-01-02 15:04:05"}}</p>
        <p><strong>执行耗时:</strong> {{.Summary.Duration}}</p>
        <p><strong>报告生成时间:</strong> {{.GeneratedAt.Format "2006-01-02 15:04:05"}}</p>
    </div>

    <div class="summary">
        <div class="summary-item">
            <h3>{{.Summary.TotalRules}}</h3>
            <p>总检查项</p>
        </div>
        <div class="summary-item">
            <h3 style="color: #28a745;">{{.Summary.PassedRules}}</h3>
            <p>通过项 ({{printf "%.1f" .Summary.PassRate}}%)</p>
        </div>
        <div class="summary-item">
            <h3 style="color: #dc3545;">{{.Summary.FailedRules}}</h3>
            <p>失败项 ({{printf "%.1f" .Summary.FailRate}}%)</p>
        </div>
        <div class="summary-item">
            <h3 style="color: #ffc107;">{{.Summary.ErrorRules}}</h3>
            <p>错误项 ({{printf "%.1f" .Summary.ErrorRate}}%)</p>
        </div>
    </div>

    <div class="results">
        <h2>详细检查结果</h2>
        {{range .Session.Results}}
        <div class="result-item {{if eq .Status "passed"}}passed{{else if eq .Status "failed"}}failed{{else}}error{{end}}">
            <div class="rule-name">{{.Rule.Name}}</div>
            <div class="rule-desc">{{.Rule.Description}}</div>
            <p><strong>状态:</strong> 
                {{if eq .Status "passed"}}✅ 通过
                {{else if eq .Status "failed"}}❌ 失败
                {{else}}⚠️ 错误{{end}}
            </p>
            <p><strong>分类:</strong> {{.Rule.Category}}</p>
            <p><strong>等级:</strong> {{.Rule.Level}}</p>
            <p><strong>严重程度:</strong> {{.Rule.Severity}}</p>
            {{if .Error}}
            <p><strong>错误信息:</strong> {{.Error}}</p>
            {{end}}
            {{if .Output}}
            <p><strong>输出结果:</strong></p>
            <div class="rule-output">{{.Output}}</div>
            {{end}}
            {{if .Suggestion}}
            <p><strong>修复建议:</strong> {{.Suggestion}}</p>
            {{end}}
        </div>
        {{end}}
    </div>
</body>
</html>`

	tmpl, err := template.New("report").Parse(htmlTemplate)
	if err != nil {
		return err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return err
	}

	return os.WriteFile(outputPath, buf.Bytes(), 0644)
}

func (g *Generator) GenerateJSON(session *checker.CheckSession, outputPath string) error {
	data := g.prepareReportData(session)

	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(outputPath, jsonData, 0644)
}

func (g *Generator) prepareReportData(session *checker.CheckSession) *ReportData {
	summary := &ReportSummary{
		TotalRules:   session.TotalRules,
		PassedRules:  session.PassedRules,
		FailedRules:  session.FailedRules,
		ErrorRules:   session.ErrorRules,
		SkippedRules: 0,
		Duration:     session.Duration.String(),
	}

	if session.TotalRules > 0 {
		summary.PassRate = float64(session.PassedRules) / float64(session.TotalRules) * 100
		summary.FailRate = float64(session.FailedRules) / float64(session.TotalRules) * 100
		summary.ErrorRate = float64(session.ErrorRules) / float64(session.TotalRules) * 100
	}

	return &ReportData{
		Session:     session,
		GeneratedAt: time.Now(),
		Summary:     summary,
	}
}

func (g *Generator) GetSupportedFormats() []string {
	return []string{"html", "json"}
}
