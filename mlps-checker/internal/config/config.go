package config

import (
	"encoding/json"
	"os"
	"path/filepath"
)

type Config struct {
	AppName     string `json:"app_name"`
	Version     string `json:"version"`
	WindowTitle string `json:"window_title"`
	WindowSize  struct {
		Width  int `json:"width"`
		Height int `json:"height"`
	} `json:"window_size"`
	CheckTimeout    int    `json:"check_timeout"`
	MaxConcurrency  int    `json:"max_concurrency"`
	RulesPath       string `json:"rules_path"`
	ReportsPath     string `json:"reports_path"`
	LogLevel        string `json:"log_level"`
}

var DefaultConfig = Config{
	AppName:     "Linux等保基线核查工具",
	Version:     "1.0.0",
	WindowTitle: "Linux等保基线核查工具 v1.0.0",
	WindowSize: struct {
		Width  int `json:"width"`
		Height int `json:"height"`
	}{
		Width:  1200,
		Height: 800,
	},
	CheckTimeout:   300,
	MaxConcurrency: 10,
	RulesPath:      "rules",
	ReportsPath:    "reports",
	LogLevel:       "info",
}

func Load() (*Config, error) {
	configPath := "config.json"
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return &DefaultConfig, nil
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, err
	}

	return &config, nil
}

func (c *Config) Save() error {
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile("config.json", data, 0644)
}

func (c *Config) GetRulesPath() string {
	if filepath.IsAbs(c.RulesPath) {
		return c.RulesPath
	}
	return filepath.Join(".", c.RulesPath)
}

func (c *Config) GetReportsPath() string {
	if filepath.IsAbs(c.ReportsPath) {
		return c.ReportsPath
	}
	return filepath.Join(".", c.ReportsPath)
}
