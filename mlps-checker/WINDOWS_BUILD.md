# Windows编译指南

由于Fyne GUI框架依赖CGO和系统图形库，在无图形界面的Linux环境中无法直接编译Windows版本。以下是在Windows或有图形界面的Linux系统中编译的详细步骤。

## 方法一：在Windows系统中编译

### 环境准备

1. 安装Go 1.19+
2. 安装Git
3. 安装TDM-GCC或MinGW-w64

### 编译步骤

```cmd
# 克隆项目
git clone <项目地址>
cd mlps-checker

# 安装依赖
go mod tidy

# 安装Fyne工具
go install fyne.io/tools/cmd/fyne@latest

# 编译Windows版本
go build -o mlps-checker.exe ./cmd

# 或使用Fyne工具打包
fyne package -os windows -name mlps-checker.exe ./cmd
```

## 方法二：在Linux系统中交叉编译

### 环境准备 (Ubuntu/Debian)

```bash
# 安装基础开发工具
sudo apt-get update
sudo apt-get install -y build-essential

# 安装Go
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
export PATH=$PATH:/usr/local/go/bin

# 安装图形界面开发库
sudo apt-get install -y libgl1-mesa-dev libxrandr-dev libxcursor-dev libxinerama-dev libxi-dev libxxf86vm-dev

# 安装Windows交叉编译工具链
sudo apt-get install -y gcc-mingw-w64
```

### 编译步骤

```bash
# 进入项目目录
cd mlps-checker

# 安装依赖
go mod tidy

# 安装Fyne工具
go install fyne.io/tools/cmd/fyne@latest

# 使用Fyne工具编译Windows版本
fyne package -os windows -name mlps-checker.exe ./cmd

# 或者使用Go交叉编译
CGO_ENABLED=1 GOOS=windows GOARCH=amd64 CC=x86_64-w64-mingw32-gcc go build -o mlps-checker.exe ./cmd
```

## 方法三：使用Docker编译

创建Dockerfile：

```dockerfile
FROM golang:1.21-bullseye

# 安装依赖
RUN apt-get update && apt-get install -y \
    gcc-mingw-w64 \
    libgl1-mesa-dev \
    libxrandr-dev \
    libxcursor-dev \
    libxinerama-dev \
    libxi-dev \
    libxxf86vm-dev

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY . .

# 安装Go依赖
RUN go mod tidy

# 安装Fyne工具
RUN go install fyne.io/tools/cmd/fyne@latest

# 编译Windows版本
RUN CGO_ENABLED=1 GOOS=windows GOARCH=amd64 CC=x86_64-w64-mingw32-gcc go build -o mlps-checker.exe ./cmd
```

编译命令：

```bash
# 构建Docker镜像
docker build -t mlps-checker-builder .

# 运行容器并复制编译结果
docker run --rm -v $(pwd):/output mlps-checker-builder cp mlps-checker.exe /output/
```

## 方法四：使用GitHub Actions自动编译

创建 `.github/workflows/build.yml`：

```yaml
name: Build

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.21
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y gcc-mingw-w64 libgl1-mesa-dev libxrandr-dev libxcursor-dev libxinerama-dev libxi-dev libxxf86vm-dev
    
    - name: Build for Linux
      run: go build -o mlps-checker-linux ./cmd
    
    - name: Build for Windows
      run: |
        go install fyne.io/tools/cmd/fyne@latest
        CGO_ENABLED=1 GOOS=windows GOARCH=amd64 CC=x86_64-w64-mingw32-gcc go build -o mlps-checker-windows.exe ./cmd
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: binaries
        path: |
          mlps-checker-linux
          mlps-checker-windows.exe
```

## 常见问题

### 1. CGO编译错误

确保安装了正确的C编译器：
- Windows: TDM-GCC或MinGW-w64
- Linux交叉编译: gcc-mingw-w64

### 2. 图形库依赖错误

在Linux中编译需要安装X11开发库：
```bash
sudo apt-get install libgl1-mesa-dev libxrandr-dev libxcursor-dev libxinerama-dev libxi-dev libxxf86vm-dev
```

### 3. Fyne工具版本问题

确保使用最新版本的Fyne工具：
```bash
go install fyne.io/tools/cmd/fyne@latest
```

### 4. 内存不足

编译过程可能需要较多内存，建议至少2GB可用内存。

## 编译优化

### 减小可执行文件大小

```bash
go build -ldflags="-s -w" -o mlps-checker.exe ./cmd
```

### 启用编译优化

```bash
go build -ldflags="-s -w" -gcflags="-l=4" -o mlps-checker.exe ./cmd
```

## 测试编译结果

编译完成后，可以在Windows系统中测试：

1. 将编译好的 `mlps-checker.exe` 复制到Windows系统
2. 确保 `rules/` 目录和规则文件一同复制
3. 双击运行或在命令行中执行
4. 检查GUI界面是否正常显示
5. 测试基本功能是否正常

## 分发说明

编译完成后，建议创建一个完整的分发包：

```
mlps-checker-windows/
├── mlps-checker.exe
├── rules/
│   └── basic/
│       ├── account_rules.json
│       ├── filesystem_rules.json
│       ├── network_rules.json
│       ├── service_rules.json
│       └── audit_rules.json
├── README.md
└── LICENSE
```

这样用户可以直接解压使用，无需额外配置。
