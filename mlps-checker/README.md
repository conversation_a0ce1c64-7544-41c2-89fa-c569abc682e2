# Linux等保基线核查工具

一款基于Go语言和Fyne GUI框架开发的Linux等保基线核查工具，用于自动化检查Linux系统的等级保护合规性。

## 功能特性

- 🔍 **全面检查**: 覆盖账户管理、文件系统、网络安全、系统服务、日志审计等多个方面
- 🎯 **等保合规**: 基于等保2.0标准设计的检查规则
- 🖥️ **图形界面**: 基于Fyne框架的现代化桌面应用
- 📊 **详细报告**: 支持HTML、JSON等多种格式的检查报告
- ⚙️ **灵活配置**: 支持自定义检查规则和参数配置
- 🚀 **高性能**: 支持并发检查，提高检查效率

## 项目结构

```
mlps-checker/
├── cmd/                    # 程序入口
│   └── main.go
├── internal/               # 内部包
│   ├── gui/               # GUI界面
│   │   ├── main_window.go
│   │   └── app.go
│   ├── checker/           # 检查引擎
│   │   ├── types.go
│   │   └── engine.go
│   ├── report/            # 报告生成
│   │   └── generator.go
│   └── config/            # 配置管理
│       └── config.go
├── rules/                 # 检查规则
│   └── basic/
│       ├── account_rules.json
│       ├── filesystem_rules.json
│       ├── network_rules.json
│       ├── service_rules.json
│       └── audit_rules.json
├── templates/             # 报告模板
├── assets/               # 资源文件
├── docs/                 # 文档
├── FyneApp.toml          # Fyne应用配置
├── go.mod
└── go.sum
```

## 检查项目

### 账户管理 (Account Management)
- root账户状态检查
- 空密码账户检测
- 默认账户状态验证
- 密码策略配置检查
- 登录失败锁定策略
- sudo权限配置审查
- 用户家目录权限检查
- SSH root登录配置

### 文件系统 (File System)
- 关键文件权限检查 (/etc/passwd, /etc/shadow等)
- 世界可写文件检测
- SUID/SGID文件审查
- 临时目录权限配置
- 系统日志文件权限
- 配置文件备份清理
- 磁盘使用率监控

### 网络安全 (Network Security)
- SSH服务配置检查
- 防火墙状态验证
- 开放端口审查
- 网络安全参数配置
- ICMP重定向设置
- 源路由配置
- TCP SYN Cookies防护
- SSH协议版本和超时配置

### 系统服务 (System Services)
- 不必要服务检测
- NTP时间同步服务
- 系统日志服务状态
- 定时任务服务检查
- Web/数据库/邮件服务审查
- SNMP服务状态
- DNS服务配置
- 自启动服务数量统计

### 日志审计 (Audit & Logging)
- 审计服务状态检查
- 审计规则配置验证
- 审计日志文件检查
- 系统日志配置
- 日志轮转策略
- 登录日志记录
- 认证日志检查
- 日志存储空间监控

## 编译说明

### 环境要求

- Go 1.19+
- CGO支持
- 图形界面开发库 (Linux)
- Windows交叉编译工具链 (Windows编译)

### Linux编译

```bash
# 安装依赖 (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y gcc pkg-config libgl1-mesa-dev libxrandr-dev libxcursor-dev libxinerama-dev libxi-dev libxxf86vm-dev

# 编译
go build -o mlps-checker ./cmd
```

### Windows编译

```bash
# 安装Fyne工具
go install fyne.io/tools/cmd/fyne@latest

# 使用Fyne工具打包Windows版本
fyne package -os windows -name mlps-checker.exe ./cmd
```

或者使用Go交叉编译（需要mingw-w64）：

```bash
# 安装mingw-w64
sudo apt-get install gcc-mingw-w64

# 设置环境变量并编译
CGO_ENABLED=1 GOOS=windows GOARCH=amd64 CC=x86_64-w64-mingw32-gcc go build -o mlps-checker.exe ./cmd
```

## 使用说明

1. **启动应用**: 运行编译后的可执行文件
2. **选择检查项**: 在左侧树形结构中选择要检查的项目
3. **开始检查**: 点击工具栏的"开始检查"按钮
4. **查看结果**: 在中央面板查看检查结果，点击项目查看详细信息
5. **导出报告**: 检查完成后可导出HTML或JSON格式的报告

## 配置文件

应用启动时会自动创建 `config.json` 配置文件：

```json
{
  "app_name": "Linux等保基线核查工具",
  "version": "1.0.0",
  "window_title": "Linux等保基线核查工具 v1.0.0",
  "window_size": {
    "width": 1200,
    "height": 800
  },
  "check_timeout": 300,
  "max_concurrency": 10,
  "rules_path": "rules",
  "reports_path": "reports",
  "log_level": "info"
}
```

## 自定义规则

可以在 `rules/` 目录下添加自定义检查规则，规则文件格式为JSON：

```json
[
  {
    "id": "CUSTOM_001",
    "name": "自定义检查项",
    "description": "检查描述",
    "category": "custom",
    "level": "basic",
    "command": "检查命令",
    "expected": "期望结果",
    "pattern": "正则表达式",
    "severity": "high",
    "solution": "修复建议",
    "reference": "参考标准",
    "enabled": true
  }
]
```

## 技术架构

- **开发语言**: Go 1.19+
- **GUI框架**: Fyne v2.6+
- **配置格式**: JSON
- **报告生成**: HTML模板
- **并发处理**: Goroutines + Channel
- **跨平台**: 支持Linux、Windows、macOS

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
